export interface Customer {
  id: string;
  name: string;
  phone: string;
  address?: string;
  email?: string;
}

export interface InvoiceItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  price: number;
  amount: number;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  date: string;
  customer_id: string;
  customer?: Customer;
  items?: InvoiceItem[];
  subtotal: number;
  discount: number;
  discount_percentage: number;
  total: number;
  status: "paid" | "unpaid" | "pending";
  notes?: string;
  order_received_date?: string;
  delivery_date?: string;
  type?: string;
}

// Database response types (with null instead of undefined)
export interface CustomerDB {
  id: string;
  name: string;
  phone: string;
  address: string | null;
  email: string | null;
  created_at: string;
  updated_at: string;
}

export interface InvoiceDB {
  id: string;
  invoice_number: string;
  date: string;
  customer_id: string;
  subtotal: number;
  discount: number;
  discount_percentage: number;
  total: number;
  status: string;
  notes: string | null;
  order_received_date: string | null;
  delivery_date: string | null;
  type: string | null;
  created_at: string;
  updated_at: string;
}

export interface InvoiceItemDB {
  id: string;
  invoice_id: string;
  name: string;
  quantity: number;
  unit: string;
  price: number;
  amount: number;
  created_at: string;
  updated_at: string;
}

// Utility types for API operations
export type CreateCustomerInput = Omit<Customer, "id">;
export type UpdateCustomerInput = Partial<Omit<Customer, "id">>;
export type CreateInvoiceInput = Omit<Invoice, "id" | "invoice_number">;
export type UpdateInvoiceInput = Partial<Omit<Invoice, "id" | "invoice_number">>;

// Helper functions to convert between DB and app types
export const convertCustomerFromDB = (dbCustomer: CustomerDB): Customer => ({
  id: dbCustomer.id,
  name: dbCustomer.name,
  phone: dbCustomer.phone,
  address: dbCustomer.address || undefined,
  email: dbCustomer.email || undefined,
});

export const convertInvoiceFromDB = (
  dbInvoice: InvoiceDB & {
    customer?: CustomerDB;
    items?: InvoiceItemDB[]
  }
): Invoice => ({
  id: dbInvoice.id,
  invoice_number: dbInvoice.invoice_number,
  date: dbInvoice.date,
  customer_id: dbInvoice.customer_id,
  customer: dbInvoice.customer ? convertCustomerFromDB(dbInvoice.customer) : undefined,
  items: dbInvoice.items?.map(item => ({
    id: item.id,
    name: item.name,
    quantity: item.quantity,
    unit: item.unit,
    price: item.price,
    amount: item.amount,
  })),
  subtotal: dbInvoice.subtotal,
  discount: dbInvoice.discount,
  discount_percentage: dbInvoice.discount_percentage,
  total: dbInvoice.total,
  status: dbInvoice.status as "paid" | "unpaid" | "pending",
  notes: dbInvoice.notes || undefined,
  order_received_date: dbInvoice.order_received_date || undefined,
  delivery_date: dbInvoice.delivery_date || undefined,
  type: dbInvoice.type || undefined,
});
