import { supabase } from "@/integrations/supabase/client";
import type {
  CreateInvoiceInput,
  CustomerDB,
  Invoice,
  InvoiceDB,
  InvoiceItemDB,
  UpdateInvoiceInput,
} from "@/lib/types";
import { convertInvoiceFromDB } from "@/lib/types";

export const invoiceService = {
  async getAll(): Promise<Invoice[]> {
    const { data, error } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        items:invoice_items(*)
      `
      )
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching invoices:", error);
      throw error;
    }

    type InvoiceWithRelations = InvoiceDB & {
      customer?: CustomerDB;
      items?: InvoiceItemDB[];
    };
    return ((data as InvoiceWithRelations[]) || []).map(convertInvoiceFromDB);
  },

  async getById(id: string): Promise<Invoice | null> {
    const { data, error } = await supabase
      .from("invoices")
      .select(
        `
        *,
        customer:customers(*),
        items:invoice_items(*)
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching invoice:", error);
      return null;
    }

    type InvoiceWithRelations = InvoiceDB & {
      customer?: CustomerDB;
      items?: InvoiceItemDB[];
    };
    return data ? convertInvoiceFromDB(data as InvoiceWithRelations) : null;
  },

  async create(invoice: CreateInvoiceInput): Promise<Invoice> {
    // Generate invoice number
    const { data: invoiceNumberData } = await supabase.rpc(
      "generate_invoice_number"
    );

    const invoiceNumber = invoiceNumberData || `INV${Date.now()}`;

    const { data, error } = await supabase
      .from("invoices")
      .insert([
        {
          ...invoice,
          invoice_number: invoiceNumber,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating invoice:", error);
      throw error;
    }

    // Create invoice items if provided
    if (invoice.items && invoice.items.length > 0) {
      const itemsToInsert = invoice.items.map((item) => ({
        invoice_id: data.id,
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        price: item.price,
        amount: item.amount,
      }));

      const { error: itemsError } = await supabase
        .from("invoice_items")
        .insert(itemsToInsert);

      if (itemsError) {
        console.error("Error creating invoice items:", itemsError);
        throw itemsError;
      }
    }

    return convertInvoiceFromDB(data as InvoiceDB);
  },

  async update(id: string, invoice: UpdateInvoiceInput): Promise<Invoice> {
    const { data, error } = await supabase
      .from("invoices")
      .update(invoice)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating invoice:", error);
      throw error;
    }

    return convertInvoiceFromDB(data as InvoiceDB);
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase.from("invoices").delete().eq("id", id);

    if (error) {
      console.error("Error deleting invoice:", error);
      throw error;
    }
  },
};
